import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateBannerDto } from './dto/create-banner.dto';
import { UpdateBannerDto } from './dto/update-banner.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Banner } from './entities/banner.entity';
import {
  PaginateConfig,
  PaginateQuery,
  Paginated,
  paginate,
} from 'nestjs-paginate';

export const BANNER_PAGINATION_CONFIG: PaginateConfig<Banner> = {
  sortableColumns: ['id', 'title', 'description'],
  searchableColumns: ['title', 'description'],
};

@Injectable()
export class BannerService {
  constructor(
    @InjectRepository(Banner)
    private readonly bannerRepository: Repository<Banner>,
  ) {}

  create(createBannerDto: CreateBannerDto) {
    const banner = this.bannerRepository.create(createBannerDto);

    return this.bannerRepository.save(banner);
  }

  findAll(isShow?: boolean) {
    if (isShow === undefined) {
      return this.bannerRepository.find();
    }

    return this.bannerRepository.find({
      where: {
        isShow: isShow,
      },
    });
  }

  async findOne(id: number) {
    const banner = await this.bannerRepository.findOneBy({ id });

    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    return banner;
  }

  async update(id: number, updateBannerDto: UpdateBannerDto) {
    const banner = await this.bannerRepository.findOneBy({ id });
    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    return this.bannerRepository.save({ ...banner, ...updateBannerDto });
  }

  async remove(id: number) {
    const banner = await this.bannerRepository.findOneBy({ id });

    if (!banner) {
      throw new NotFoundException(`Banner #${id} not found`);
    }

    await this.bannerRepository.delete(id);

    return banner;
  }

  async datatables(query: PaginateQuery): Promise<Paginated<Banner>> {
    return paginate(query, this.bannerRepository, BANNER_PAGINATION_CONFIG);
  }
}
