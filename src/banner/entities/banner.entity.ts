import { Expose } from 'class-transformer';
import { CustomBaseEntity } from '../../common/entities';
import { imagePath } from '../../common/helper';
import { Column, Entity } from 'typeorm';

@Entity()
export class Banner extends CustomBaseEntity {
  @Column({ comment: 'Title of banner' })
  title: string;

  @Column({ nullable: true, comment: 'Banner description' })
  description: string;

  @Column({ comment: 'Banner image' })
  image: string;

  @Column({
    default: true,
    name: 'is_show',
    comment: 'Indicates if the banner is visible',
  })
  isShow: boolean;

  @Expose()
  get imageUrl() {
    return imagePath(this.image);
  }

  constructor(partial?: Partial<Banner>) {
    super();
    if (partial) {
      Object.assign(this, partial);
    }
  }
}
