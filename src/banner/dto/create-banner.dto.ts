import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class CreateBannerDto {
  @ApiProperty()
  @IsNotEmpty()
  readonly title: string;

  @ApiProperty({ required: false })
  readonly description?: string;

  @ApiProperty()
  @IsNotEmpty()
  readonly image: string;

  @ApiProperty()
  readonly isShow: boolean;

  @ApiProperty()
  @IsNotEmpty()
  readonly storeId: number;
}
