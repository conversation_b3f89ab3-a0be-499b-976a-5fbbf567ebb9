import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { FoodType } from 'src/member/entities/member.entity';

export class ReportOrderDto {
  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;

  @ApiProperty({ required: false })
  readonly branchID: number;

  @ApiProperty({ required: false })
  readonly categoryID: number;

  @ApiProperty({ required: false })
  readonly productID: number;

  @ApiProperty({ required: false })
  branchCode: string;
}
export class getOrderDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  @IsNotEmpty({ message: 'DeviceId is required' })
  readonly deviceId: number;
}

export class getOrderdeviceDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  @IsNotEmpty({ message: 'DeviceId is required' })
  readonly deviceId: number[];
}

export class getOrderDeviceDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  @IsNotEmpty({ message: 'DeviceIds is required' })
  readonly deviceIds: number[];
}

export class getOrderReserveDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;
}

export class SaleOrderSummaryDto {
  @IsNotEmpty({ message: 'Start date not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty({ message: 'End date not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;

  readonly deviceIds: number[];

  readonly paymentMethods: number[];

  @IsNotEmpty({ message: '' })
  @ApiProperty()
  includeBuffet: boolean;
}

export class HistoryReportMemberDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  @IsNotEmpty()
  readonly memberIds: number[];
}

export class GetBuffetByTypeDto {
  @ValidateIf((o) => o.foodType !== '')
  @IsOptional()
  @IsEnum(FoodType)
  foodType?: FoodType | '';

  @IsString()
  @IsNotEmpty()
  startDate: string;

  @IsString()
  @IsNotEmpty()
  endDate: string;
}

export class GetExpensesDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;
}
export class Top10Dto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  branchCodes?: string[];
}

export class SalesReportDto {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  // @IsOptional()
  // @IsString({ each: true })
  // branchCodes?: string[];

  @IsOptional()
  @IsNumber({}, { each: true })
  deviceIds?: number[];
}

export class CreditChange {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  @IsNotEmpty()
  readonly memberIds: number[];
}

export class auditlog {
  @IsNotEmpty({ message: 'Startdate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly startDate: string;

  @IsNotEmpty({ message: 'EndDate not Found ' })
  @ApiProperty({ type: Date, format: 'date' })
  readonly endDate: string;

  readonly userId: number;
}
