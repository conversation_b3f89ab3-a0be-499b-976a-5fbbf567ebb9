import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class ReportTapcardDto {
  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  startDate: string;

  @IsNotEmpty()
  @ApiProperty({ type: Date, format: 'date' })
  endDate: string;

  @ApiProperty({ required: false })
  readonly branchID: number;

  @ApiProperty({ required: false })
  readonly categoryID: number;

  @ApiProperty({ required: false })
  readonly productID: number;

  @ApiProperty({ required: false })
  Type: string;
}
