import { Controller, Get, Req, UnauthorizedException } from '@nestjs/common';
import { AppService } from './app.service';
import { Request } from 'express';
import { ApiHeader } from '@nestjs/swagger';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  @Get('/ip')
  getYourIP(@Req() req: Request) {
    console.log(req.headers);
  }

  @Get('/sync')
  @ApiHeader({ name: 'api-key' })
  async sync(@Req() req: Request) {
    // c7ef38a0594617d91138899ca6f43884724b828047b22a2d16d706d32ed58040
    if (
      req.headers['api-key'] !=
      'c7ef38a0594617d91138899ca6f43884724b828047b22a2d16d706d32ed58040'
    ) {
      throw new UnauthorizedException();
    }

    return this.appService.sync();
  }
}
