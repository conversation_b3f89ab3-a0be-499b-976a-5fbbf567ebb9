import { Category } from '../../category/entities/category.entity';
import { CustomBaseEntity } from '../../common/entities';
import { Column, Entity, Index, JoinColumn, JoinTable, ManyToMany, ManyToOne, OneToMany, } from 'typeorm';
import { OrderItem } from '../../order/entities/order-item.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { ProductAttribute } from './product-attribute.entity';
import { Unit } from '../../unit/entities/unit.entity';
import { Expose } from 'class-transformer';
import { imagePath } from '../../common/helper';
import { Branch } from '../../branch/entities/branch.entity';
import { Store } from '../../store/entities/store.entity';
import { PanelProduct } from '../../panel/entities/panel-product.entity';
import { Vendor } from '../../vendor/entities/vendor.entity';

export enum ShowType {
  COLOR = 'color',
  IMAGE = 'image',
}

@Entity()
export class Product extends CustomBaseEntity {
  @Column({ comment: 'Unique code for the product' })
  @Index({ unique: true })
  code: string; // Unique code for the product

  @Column({ comment: 'Name of the product' })
  name: string; // Name of the product

  @Column({ nullable: true, comment: 'Image URL of the product' })
  image: string; // Image URL of the product (optional)

  @Expose()
  get imageUrl(): string {
    return imagePath(this.image);
  } // Computed property to get the full image URL

  @Column('numeric', {
    transformer: new DecimalColumnTransformer(),
    comment: 'Selling price of the product',
  })
  price: number; // Selling price of the product

  @Column('numeric', {
    nullable: true,
    transformer: new DecimalColumnTransformer(),
    comment: 'Cost of the product ',
  })
  cost: number; // Cost price of the product (optional)

  @Column({
    default: false,
    comment: 'Indicates if the product is active',
  })
  active: boolean; // Indicates if the product is active

  @Column({ nullable: true, comment: 'Barcode of the product' })
  barcode: string; // Barcode of the product (optional)

  @Column({ nullable: true, comment: 'Additional remarks about the product' })
  remark: string; // Additional remarks about the product (optional)

  // @Column({
  //   default: false,
  //   comment: 'Status of the remark (true if remark is important)',
  // })
  // remarkStatus: boolean; // Status of the remark (true if remark is important)

  // @Column({
  //   default: false,
  //   comment: 'Indicates if the product is subject to VAT',
  // })
  // vatStatus: boolean; // Indicates if the product is subject to VAT

  @ManyToOne(() => Category, (_) => _.products)
  @JoinColumn({ name: 'category_id' })
  category: Category;

  @OneToMany(() => OrderItem, (_) => _.product)
  orderItems: OrderItem[];

  @OneToMany(
    () => ProductAttribute,
    (productAttribute) => productAttribute.product,
    {
      cascade: true,
    },
  )
  productAttributes: ProductAttribute[];

  @ManyToOne(() => Unit, (_) => _.products)
  @JoinColumn({ name: 'unit_id' })
  unit: Unit;

  @ManyToMany(() => Branch, (branch) => branch.products)
  @JoinTable()
  branches: Branch[];

  @Column({ type: 'enum', enum: ShowType, nullable: true })
  showType: ShowType

  @Column({ nullable: true })
  color: string;

  @ManyToOne(() => Store, (_) => _.products)
  store: Store;

  @OneToMany(() => PanelProduct, (_) => _.product)
  panelProducts: PanelProduct[];

    @ManyToMany(() => Vendor, (vendor) => vendor.products)
    @JoinTable({ name: 'product_vendor' })
    vendors: Vendor[];
}
