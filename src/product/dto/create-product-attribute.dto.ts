import { IsNotEmpty } from 'class-validator';
import { ProductAttributeType } from '../entities/product-attribute.entity';

export class CreateProductAttributeDto {
  readonly id?: number;

  @IsNotEmpty({ message: 'Name is required' })
  readonly name: string;

  @IsNotEmpty({ message: 'Type is required' })
  readonly type: ProductAttributeType;

  readonly attributeValues: CreateProductAttributeValueDto[];
}

export class CreateProductAttributeValueDto {
  readonly id?: number;

  @IsNotEmpty({ message: 'Name is required' })
  readonly name: string;

  @IsNotEmpty({ message: 'Price is required' })
  readonly price: number;
}
