import { Injectable } from '@nestjs/common';
import { Product } from './product/entities/product.entity';
import { Category } from './category/entities/category.entity';
import { Unit } from './unit/entities/unit.entity';
// import { ProductAttribute } from './product/entities/product-attribute.entity';
// import { ProductAttributeValue } from './product/entities/product-attribute-value.entity';
import { Branch } from './branch/entities/branch.entity';
import { Panel } from './panel/entities/panel.entity';
import { PaymentMethod } from './payment-method/entities/payment-method.entity';
import { Member } from './member/entities/member.entity';
import { Device } from './device/entities/device.entity';

@Injectable()
export class AppService {
  getHello(): string {
    return 'POS API Version 1.0.0';
  }

  async sync() {
    const [
      branches,
      categories,
      devices,
      members,
      products,
      // productAttributes,
      // productAttributeValues,
      units,
      panels,
      paymentMethods,
    ] = await Promise.all([
      Branch.find({ withDeleted: true }),
      Category.find({ withDeleted: true }),
      Device.find({
        withDeleted: true,
        loadRelationIds: { relations: ['branch'] },
      }),
      Member.find({
        withDeleted: true,
        loadRelationIds: { relations: ['grade'] },
      }),
      Product.find({
        withDeleted: true,
        loadRelationIds: { relations: ['category', 'unit'] },
      }),
      // ProductAttribute.find({ withDeleted: true, loadRelationIds: { relations: ['product'] } }),
      // ProductAttributeValue.find({ withDeleted: true, loadRelationIds: true }),
      Unit.find({ withDeleted: true }),
      Panel.find({
        withDeleted: true,
        loadRelationIds: { relations: ['branch'] },
      }),
      PaymentMethod.find({ withDeleted: true }),
    ]);

    return {
      branches,
      categories,
      devices,
      members,
      products,
      // productAttributes,
      // productAttributeValues,
      units,
      panels,
      paymentMethods,
    };
  }
}
