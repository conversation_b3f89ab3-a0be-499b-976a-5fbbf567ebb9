import { Order } from '../../order/entities/order.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { CustomBaseEntity } from '../../common/entities';
import {
  Column,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { Payment } from '../../payment/entities/payment.entity';
import { AuditLog } from '../../auditlog/entities/auditlog.entity';
import { Expose } from 'class-transformer';

export enum CardType {
  STUDENT = 'Student',
  TEACHER = 'Teacher',
  STAFF = 'Staff',
}

export enum FoodType {
  Western = 'Western',
  Asian = 'Asian',
  Noodle = 'Noodle',
}
@Entity()
export class Member extends CustomBaseEntity {
  @Column({ unique: true, comment: 'Unique code for the Member' })
  @Index()
  code: string;

  @Column({ comment: 'Member Firstname' })
  firstname: string;

  @Column({ nullable: true, comment: 'Member Middlename' })
  middlename: string;

  @Column({ nullable: true, comment: 'Member Lastname' })
  lastname: string;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
  })
  wallet: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
  })
  creditEL2: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
  })
  creditEL4: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Current credit amount used',
  })
  credit: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    comment: 'Monthly credit amount received',
  })
  defaultcredit: number;

  @Column({
    type: 'numeric',
    default: 0,
    transformer: new DecimalColumnTransformer(),
    nullable: true,
    comment: 'Limit credit for this member',
  })
  limitcredit: number;

  @Column({ default: true, comment: 'Indicates if the Member is Active' })
  active: boolean;
  member: any;

  isActive(): boolean {
    return this.active;
  }
  // @OneToOne(() => Card, { cascade: true })  // Cascade operations to Card
  // @JoinColumn({ name: 'card_id' })
  // card: Card;

  @OneToMany(() => Order, (_) => _.member)
  orders: Order[];

  @OneToMany(() => Payment, (_) => _.member)
  payments: Payment[];

  @OneToMany(() => AuditLog, (auditLog) => auditLog.member)
  auditLogs: AuditLog[];

  @Column({ nullable: true, comment: 'Serialnumber member card' })
  sn: string;

  @Column({ name: 'card_type', type: 'enum', enum: CardType, nullable: true })
  cardType: CardType;

  // @OneToMany(() => TapLog, (_) => _.member)
  // tapLogs: TapLog[];

  @Column({
    type: 'date',
    name: 'active_date',
    nullable: true,
    comment: 'Date when the card becomes active',
  })
  activeDate: Date;

  @Expose()
  get fullName(): string {
    return `${this.firstname} ${this.middlename} ${this.lastname}`;
  }

  @Expose()
  get remainCredit(): number {
    return this.limitcredit - this.credit;
  }
}
