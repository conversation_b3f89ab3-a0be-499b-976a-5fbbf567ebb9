import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
// import { CardType } from "src/card/entities/card.entity";
import { CardType } from '../entities/member.entity';

export class CreateMemberDto {
  @IsNotEmpty({ message: 'Code is required' })
  readonly code: string;

  @IsNotEmpty({ message: 'Firstname is required' })
  readonly firstname: string;

  readonly middlename?: string;

  readonly lastname?: string;

  readonly active: boolean;

  readonly cardSN?: string;

  readonly cardType?: CardType;

  @ApiProperty({ type: Date, format: 'date' })
  readonly activeDate?: Date;

  @ApiProperty()
  // @ValidateIf(o => o.cardType === 'Student')
  // @IsNotEmpty()
  readonly gradeId: number;

  @ApiProperty()
  @IsNotEmpty()
  readonly limitcredit: number;
}
