import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Header,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  Patch,
  Post,
  Put,
  Req,
  Res,
  StreamableFile,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from 'src/auth/decorators/auth.decorator';
import { Readable } from 'stream';
import { CreateMemberDto } from './dto/create-member.dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import {
  MEMBER_PAGINATION_CONFIG,
  MemberService,
} from './member.service';
import { Response } from 'express';
import { TopupMemberDto } from './dto/topup-member.dto';
import { FindMemberDto } from './dto/find-member.dto';
import { CardMemberDto } from './dto/card-member.dto';
import { ReportTopupDto } from 'src/report/dto/report-topup.dto';
import { DateTime } from 'luxon';

@Controller('member')
@ApiTags('สมาชิก')
export class MemberController {
  constructor(private readonly memberService: MemberService) {}
  @Auth()
  @Post('/export/excel')
  async reportOrder(
    @Body() payload: any,
    @Res({ passthrough: true }) res: Response,
  ) {
    const content: any = await this.memberService.exportMember();

    const file = Readable.from(content);

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="export-member.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition',
    });

    return new StreamableFile(file);
  }
  @Auth()
  @Post('/export/credit/excel')
  async exportMemberCredit(
    @Body() payload: any,
    @Res({ passthrough: true }) res: Response,
  ) {
    const content: any = await this.memberService.exportCredit();

    const file = Readable.from(content);

    res.set({
      'Content-Type':
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'Content-Disposition': `attachment; filename="export-member-credit.xlsx"`,
      'Access-Control-Expose-Headers': 'Content-Disposition',
    });

    return new StreamableFile(file);
  }
  @Auth()
  @Post('import-excel')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async importEmployee(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.memberService.import(file);
  }
  
  @Auth()
  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiPaginationQuery(MEMBER_PAGINATION_CONFIG)
  datatables(@Paginate() query: PaginateQuery) {
    return this.memberService.datatables(query);
  }
  @Auth()
  @Post('/find')
  @ApiOperation({ summary: 'ค้นหาพนักงานด้วย รหัสพนักงาน' })
  findMember(@Body() payload: FindMemberDto) {
    return this.memberService.findMember(payload.memberNo);
  }
  @Auth()
  @Post('/:memberId/register')
  @ApiOperation({ summary: 'ลงทะเบียนพนักงาน' })
  register(@Param('memberId') id: string, @Body() payload: CardMemberDto) {
    return this.memberService.registerMember(
      +id,
      payload.cardSN,
      payload.gradeId,
      payload.cardType,
    );
  }
  @Auth()
  @Post('/:memberId/payment')
  @ApiOperation({ summary: 'ประวัติการใช้จ่าย' })
  printPayment(@Param('memberId') id: string, @Body() payload: ReportTopupDto) {
    const start = DateTime.fromISO(payload.startDate).startOf('day').toJSDate();
    const end = DateTime.fromISO(payload.endDate).endOf('day').toJSDate();

    return this.memberService.printPayment(+id, start, end);
  }
  @Auth()
  @Post()
  create(@Body() createMemberDto: CreateMemberDto) {
    return this.memberService.create(createMemberDto);
  }

  @Auth()
  @Get()
  findAll() {
    return this.memberService.findAll();
  }
  @Auth()
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.memberService.findOne(+id);
  }
  @Auth()
  @Put(':id')
  update(@Param('id') id: string, @Body() updateMemberDto: UpdateMemberDto) {
    return this.memberService.update(+id, updateMemberDto);
  }
  @Auth()
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.memberService.remove(+id);
  }
  // @Auth()
  // @Post('/:id/add-credit')
  // @ApiOperation({ summary: 'Add specific amount of credit to a member' })
  // @ApiResponse({ status: 200, description: 'Credit added successfully' })
  // @ApiResponse({ status: 400, description: 'Credit limit exceeded' })
  // @ApiBody({
  //   description: 'Amount of credit to add',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       amount: {
  //         type: 'number',
  //         example: 0,
  //         description: 'The amount of credit to be added',
  //       },
  //     },
  //     required: ['amount'],
  //   },
  // })
  // async addCredit(@Param('id') id: number, @Body() body: { amount: number }) {
  //   const { amount } = body;

  //   // Ensure amount is a positive number
  //   if (amount <= 0) {
  //     throw new BadRequestException('Amount must be a positive number');
  //   }

  //   await this.memberService.addCredit(id, amount);
  //   return { message: 'Credit added successfully' };
  // }

  // @Auth()
  // @Put(':id/limit-credit')
  // @ApiOperation({ summary: 'Update member credit limit' })
  // @ApiParam({
  //   name: 'id',
  //   type: 'number',
  //   description: 'ID of the member to update',
  // })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       limitcredit: { type: 'number', example: 500 },
  //       status: { type: 'boolean', example: true },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Member credit limit updated successfully.',
  // })
  // @ApiResponse({ status: 404, description: 'Member not found.' })
  // @ApiResponse({ status: 400, description: 'Invalid credit limit value.' })
  // async updateMemberLimitCredit(
  //   @Param('id') id: number,
  //   @Body('limitcredit') newLimitCredit: number,
  //   @Body('status') status: boolean,
  //   @Req() req,
  // ) {
  //   const userId = req.user.sub.toString();
  //   const username = req.user.username;

  //   console.log(req.user);

  //   return await this.memberService.updateMemberLimitCredit(
  //     id,
  //     newLimitCredit,
  //     userId,
  //     username,
  //     status,
  //   );
  // }

  // @Auth()
  // @Put(':id/permanent-limit-credit')
  // @ApiOperation({ summary: 'Update member credit limit permanent' })
  // @ApiParam({
  //   name: 'id',
  //   type: 'number',
  //   description: 'ID of the member to update',
  // })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       limitcredit: { type: 'number', example: 500 },
  //     },
  //   },
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Member credit limit permanent updated successfully.',
  // })
  // @ApiResponse({ status: 404, description: 'Member not found.' })
  // @ApiResponse({ status: 400, description: 'Invalid credit limit value.' })
  // async updateMemberPermanentLimitCredit(
  //   @Param('id') id: number,
  //   @Body('limitcredit') newLimitCredit: number,
  //   @Req() req,
  // ) {
  //   const userId = req.user.sub;
  //   const username = req.user.username;

  //   return await this.memberService.updateMemberPermanentLimitCredit(
  //     id,
  //     newLimitCredit,
  //     userId,
  //     username,
  //   );
  // }

  @Get('/balance/:cardSn')
  @ApiOperation({
    summary: 'Get balance details of a member by card serial number',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns the credit, limitcredit, and available balance of the member',
    schema: {
      type: 'object',
      properties: {
        credit: {
          type: 'number',
          example: 50,
          description: 'The current credit balance of the member',
        },
        limitcredit: {
          type: 'number',
          example: 1000,
          description: 'The credit limit of the member',
        },
        availableBalance: {
          type: 'number',
          example: 950,
          description: 'The available balance (limitcredit - credit)',
        },
      },
    },
  })
  async getBalance(@Param('cardSn') cardSn: string) {
    const balance = await this.memberService.getBalanceByCardSn(cardSn);
    if (!balance) {
      throw new NotFoundException('Member not found');
    }
    return balance;
  }

  // @Post('Move-Up-Grade')
  // @ApiOperation({ summary: '' })
  // async Moveupgrade(@Req() req) {
  //   const userId = req.user.sub;
  //   return this.memberService.Moveupgrade(userId);
  // }

  // @Post('forcereset-credit') // Define the HTTP method and route
  // @HttpCode(HttpStatus.OK) // Set the HTTP status code to return on success
  // @ApiOperation({ summary: 'Force reset credit for all members' }) // Swagger operation description
  // @ApiResponse({ status: 200, description: 'Credit reset successfully' }) // Swagger response for success
  // @ApiResponse({ status: 500, description: 'Internal server error' }) // Swagger response for errors
  // async forceresetCredit(): Promise<void> {
  //   await this.memberService.forceresetCredit(); // Call the service method
  // }

  // @Post('ResetAllLimitcredit') // Define the HTTP method and route
  // @HttpCode(HttpStatus.OK) // Set the HTTP status code to return on success
  // @ApiOperation({ summary: 'Force reset creditlimit for all members' }) // Swagger operation description
  // @ApiResponse({ status: 200, description: 'Credit reset successfully' }) // Swagger response for success
  // @ApiResponse({ status: 500, description: 'Internal server error' }) // Swagger response for errors
  // async ResetAllLimitcredit(): Promise<void> {
  //   await this.memberService.ResetAllLimitcredit(); // Call the service method
  // }
}
